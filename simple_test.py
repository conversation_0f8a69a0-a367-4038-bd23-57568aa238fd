"""
Simple test to verify the Shopify scraper works
"""

from shopify_order_scraper import ShopifyOrderScraper
import time

def main():
    print("🧪 Simple Shopify Scraper Test")
    print("=" * 40)
    
    # Create scraper instance
    scraper = ShopifyOrderScraper(headless=False, slow_mode=True)
    
    try:
        # Setup driver
        print("1. Setting up Chrome WebDriver...")
        scraper.setup_driver()
        print("✓ WebDriver setup successful!")
        
        # Navigate to Shopify
        print("\n2. Navigating to Shopify orders page...")
        url = "https://admin.shopify.com/store/better-value-pharmacy-online/orders"
        success = scraper.navigate_to_shopify_orders(url)
        
        if success:
            print("✓ Navigation successful!")
            print("\n🔐 Please log in to Shopify in the browser window that opened.")
            print("Once you're logged in and can see the orders page, press Enter to continue...")
            input()
            
            print("\n3. Testing search functionality...")
            search_success = scraper.search_for_order("18198")
            
            if search_success:
                print("✓ Search completed!")
                print("\n4. Waiting for results...")
                time.sleep(5)
                
                print("\n5. Current page URL:", scraper.driver.current_url)
                print("6. Current page title:", scraper.driver.title)
                
                print("\n✅ Basic test completed successfully!")
                print("🔍 Check the browser to see if the search worked.")
                print("Press Enter to close the browser...")
                input()
            else:
                print("❌ Search failed")
        else:
            print("❌ Navigation failed")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
    finally:
        print("\n🧹 Cleaning up...")
        scraper.close()
        print("✓ Test completed")

if __name__ == "__main__":
    main()
