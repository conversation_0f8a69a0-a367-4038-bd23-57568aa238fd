"""
Step 1 Only: Open Chrome and Navigate to Shopify
This script only does the first step - opening Chrome with your existing profile and navigating to the Shopify orders page
"""

from shopify_order_scraper import ShopifyOrderScraper
import time

def main():
    print("🚀 Step 1: Open Chrome and Navigate to Shopify")
    print("=" * 60)
    print("This will:")
    print("1. Open Chrome using your existing profile (so you stay logged in)")
    print("2. Navigate to the Shopify orders page")
    print("3. Wait for you to verify everything looks good")
    print("\nPress Enter to start...")
    input()
    
    # Create scraper with existing profile
    scraper = ShopifyOrderScraper(
        headless=False,           # Show the browser window
        slow_mode=True,           # Add delays for verification
        use_existing_profile=True # Use your existing Chrome profile
    )
    
    try:
        print("\n📱 Step 1a: Setting up Chrome WebDriver...")
        scraper.setup_driver()
        print("✅ Chrome browser opened!")
        
        print("\n🌐 Step 1b: Navigating to Shopify orders page...")
        shopify_url = "https://admin.shopify.com/store/better-value-pharmacy-online/orders"
        
        print(f"🔗 Going to: {shopify_url}")
        success = scraper.navigate_to_shopify_orders(shopify_url)
        
        if success:
            print("✅ Navigation completed!")
            
            # Wait a moment for page to load
            time.sleep(3)
            
            print(f"\n📊 Current URL: {scraper.driver.current_url}")
            print(f"📊 Page Title: {scraper.driver.title}")
            
            print("\n🔍 VERIFICATION:")
            print("- Look at the Chrome browser window that opened")
            print("- You should see the Shopify admin page")
            print("- If you were already logged in, you should see the orders page")
            print("- If not logged in, you'll see the login page")
            print("- The browser should show 'Chrome is being controlled by automated test software'")
            
            print("\n✅ Step 1 Complete!")
            print("The browser will stay open so you can:")
            print("1. Log in if needed")
            print("2. Navigate to the orders page if needed")
            print("3. Verify you can see the search box")
            
            print("\nPress Enter when you're ready to close the browser...")
            input()
            
        else:
            print("❌ Navigation failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        
    finally:
        print("\n🧹 Closing browser...")
        scraper.close()
        print("✅ Done!")

if __name__ == "__main__":
    main()
