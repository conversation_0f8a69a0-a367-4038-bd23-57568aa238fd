"""
Step-by-step Shopify Order Scraper Test
This script breaks down the process into individual steps for verification
"""

from shopify_order_scraper import ShopifyOrderScraper
import time

def step_1_open_browser():
    """Step 1: Open Chrome browser with existing profile"""
    print("🚀 STEP 1: Opening Chrome Browser")
    print("=" * 50)
    
    # Create scraper with existing profile
    scraper = ShopifyOrderScraper(
        headless=False, 
        slow_mode=True, 
        use_existing_profile=True
    )
    
    try:
        print("📱 Setting up Chrome WebDriver...")
        scraper.setup_driver()
        
        print("✅ Chrome browser opened successfully!")
        print("🔍 You should see a Chrome window open.")
        print("📝 Notice: 'Chrome is being controlled by automated test software'")
        
        print("\nPress Enter to continue to Step 2...")
        input()
        
        return scraper
        
    except Exception as e:
        print(f"❌ Step 1 failed: {e}")
        return None

def step_2_navigate_to_shopify(scraper):
    """Step 2: Navigate to Shopify orders page"""
    print("\n🌐 STEP 2: Navigating to Shopify Orders Page")
    print("=" * 50)
    
    shopify_url = "https://admin.shopify.com/store/better-value-pharmacy-online/orders"
    
    try:
        print(f"🔗 Navigating to: {shopify_url}")
        success = scraper.navigate_to_shopify_orders(shopify_url)
        
        if success:
            print("✅ Navigation successful!")
            print("🔍 Check the browser - you should see the Shopify admin page")
            print("📝 If you're not logged in, you'll see the login page")
            print("📝 If you're already logged in, you'll see the orders page")
            
            print(f"\n📊 Current URL: {scraper.driver.current_url}")
            print(f"📊 Page Title: {scraper.driver.title}")
            
            print("\nPress Enter to continue to Step 3...")
            input()
            return True
        else:
            print("❌ Navigation failed")
            return False
            
    except Exception as e:
        print(f"❌ Step 2 failed: {e}")
        return False

def step_3_verify_page_ready(scraper):
    """Step 3: Verify we're on the right page and ready to search"""
    print("\n✅ STEP 3: Verify Page is Ready")
    print("=" * 50)
    
    try:
        print("🔍 Checking current page status...")
        print(f"📊 Current URL: {scraper.driver.current_url}")
        print(f"📊 Page Title: {scraper.driver.title}")
        
        # Check if we're on a Shopify page
        if "shopify.com" in scraper.driver.current_url:
            print("✅ We're on a Shopify page")
            
            # Check if we can see orders-related content
            page_source = scraper.driver.page_source.lower()
            if "order" in page_source or "search" in page_source:
                print("✅ Page contains order/search related content")
            else:
                print("⚠️ Page might not be the orders page yet")
            
            print("\n🔐 MANUAL CHECK REQUIRED:")
            print("1. Look at the browser window")
            print("2. Make sure you're logged in to Shopify")
            print("3. Make sure you can see the orders page")
            print("4. Look for a search box on the page")
            
            print("\nAre you on the orders page and can see a search box? (y/n): ", end="")
            response = input().strip().lower()
            
            if response == 'y':
                print("✅ Ready to proceed with search!")
                return True
            else:
                print("⚠️ Please navigate to the orders page manually, then run the next step")
                return False
        else:
            print("❌ Not on a Shopify page")
            return False
            
    except Exception as e:
        print(f"❌ Step 3 failed: {e}")
        return False

def step_4_search_for_order(scraper):
    """Step 4: Search for the specific order"""
    print("\n🔍 STEP 4: Searching for Order 18198")
    print("=" * 50)
    
    order_number = "18198"
    
    try:
        print(f"🔍 Searching for order: {order_number}")
        success = scraper.search_for_order(order_number)
        
        if success:
            print("✅ Search completed!")
            print("🔍 Check the browser to see the search results")
            
            # Wait a bit for results to load
            print("⏳ Waiting for search results to load...")
            time.sleep(5)
            
            print(f"📊 Current URL: {scraper.driver.current_url}")
            print(f"📊 Page Title: {scraper.driver.title}")
            
            print("\nDid the search work? Can you see order 18198? (y/n): ", end="")
            response = input().strip().lower()
            
            if response == 'y':
                print("✅ Search successful!")
                return True
            else:
                print("⚠️ Search might not have worked as expected")
                return False
        else:
            print("❌ Search failed")
            return False
            
    except Exception as e:
        print(f"❌ Step 4 failed: {e}")
        return False

def step_5_extract_information(scraper):
    """Step 5: Extract order information"""
    print("\n📊 STEP 5: Extracting Order Information")
    print("=" * 50)
    
    try:
        print("📊 Extracting order information from current page...")
        order_info = scraper.extract_order_information()
        
        print("✅ Information extracted!")
        print("\n📋 Preview of extracted data:")
        print("-" * 40)
        print(f"URL: {order_info.get('url', 'N/A')}")
        print(f"Title: {order_info.get('title', 'N/A')}")
        print(f"Order Details: {order_info.get('order_details', {})}")
        print(f"Customer Info Keys: {list(order_info.get('customer_info', {}).keys())}")
        print(f"Items Found: {len(order_info.get('items', []))}")
        
        # Save the data
        filename = scraper.save_order_data(order_info)
        if filename:
            print(f"\n💾 Data saved to: {filename}")
        
        print("\nPress Enter to finish...")
        input()
        
        return True
        
    except Exception as e:
        print(f"❌ Step 5 failed: {e}")
        return False

def main():
    """Run the step-by-step test"""
    print("🧪 Shopify Order Scraper - Step by Step Test")
    print("=" * 60)
    print("This will guide you through each step of the scraping process")
    print("You can verify each step works before proceeding to the next")
    print("\nPress Enter to start...")
    input()
    
    scraper = None
    
    try:
        # Step 1: Open browser
        scraper = step_1_open_browser()
        if not scraper:
            return
        
        # Step 2: Navigate to Shopify
        if not step_2_navigate_to_shopify(scraper):
            return
        
        # Step 3: Verify page is ready
        if not step_3_verify_page_ready(scraper):
            print("\n⚠️ You can manually navigate to the orders page and then run step 4 separately")
            print("Press Enter to continue anyway...")
            input()
        
        # Step 4: Search for order
        if not step_4_search_for_order(scraper):
            print("\n⚠️ Search didn't work as expected, but continuing to extraction...")
        
        # Step 5: Extract information
        step_5_extract_information(scraper)
        
        print("\n🎉 All steps completed!")
        
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
    finally:
        if scraper:
            print("\n🧹 Cleaning up...")
            print("Press Enter to close the browser...")
            input()
            scraper.close()
            print("✅ Browser closed")

if __name__ == "__main__":
    main()
