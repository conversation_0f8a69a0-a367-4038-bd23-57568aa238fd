"""
Shopify Order Scraper
A web automation tool to search for specific orders in Shopify admin and extract order information.
"""

import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
import platform
from bs4 import BeautifulSoup
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class ShopifyOrderScraper:
    def __init__(self, headless=False, slow_mode=True, use_existing_profile=True):
        """
        Initialize the Shopify Order Scraper

        Args:
            headless (bool): Run browser in headless mode
            slow_mode (bool): Add delays between actions for verification
            use_existing_profile (bool): Use existing Chrome profile (stays logged in)
        """
        self.headless = headless
        self.slow_mode = slow_mode
        self.use_existing_profile = use_existing_profile
        self.driver = None
        self.wait = None

    def _get_chrome_user_data_dir(self):
        """Get the Chrome user data directory path"""
        try:
            if platform.system() == "Windows":
                # Common Windows Chrome profile locations
                possible_paths = [
                    os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\User Data"),
                    os.path.expanduser("~\\AppData\\Roaming\\Google\\Chrome\\User Data"),
                    "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data"
                ]

                for path in possible_paths:
                    expanded_path = os.path.expandvars(path)
                    if os.path.exists(expanded_path):
                        return expanded_path

            elif platform.system() == "Darwin":  # macOS
                path = os.path.expanduser("~/Library/Application Support/Google/Chrome")
                if os.path.exists(path):
                    return path

            elif platform.system() == "Linux":
                path = os.path.expanduser("~/.config/google-chrome")
                if os.path.exists(path):
                    return path

        except Exception as e:
            print(f"⚠️ Error finding Chrome profile: {e}")

        return None
        
    def setup_driver(self):
        """Setup Chrome WebDriver with appropriate options"""
        try:
            chrome_options = Options()

            if self.headless:
                chrome_options.add_argument("--headless")

            # Add options for better compatibility
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--allow-running-insecure-content")

            # Use existing Chrome profile if requested
            if self.use_existing_profile:
                # Get the default Chrome user data directory
                user_data_dir = self._get_chrome_user_data_dir()
                if user_data_dir:
                    chrome_options.add_argument(f"--user-data-dir={user_data_dir}")
                    chrome_options.add_argument("--profile-directory=Default")
                    print(f"✓ Using existing Chrome profile: {user_data_dir}")
                else:
                    print("⚠️ Could not find Chrome profile, using new session")

            # User agent to avoid detection
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # Try to setup service with better error handling
            print("📥 Downloading/setting up Chrome WebDriver...")

            # Try local chromedriver.exe first
            if os.path.exists("chromedriver.exe"):
                print("✓ Using local chromedriver.exe")
                service = Service("chromedriver.exe")
            else:
                try:
                    # Try with webdriver-manager
                    print("📥 Using webdriver-manager...")
                    service = Service(ChromeDriverManager().install())
                    print("✓ WebDriver manager successful")
                except Exception as e:
                    print(f"⚠️ WebDriver manager failed: {e}")
                    print("🔄 Trying system PATH...")

                    # Fallback: try system PATH
                    service = Service()  # Let selenium find chromedriver in PATH

            # Initialize driver
            print("🚀 Starting Chrome browser...")
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.wait = WebDriverWait(self.driver, 20)

            print("✓ Chrome WebDriver initialized successfully")

        except Exception as e:
            print(f"❌ Failed to setup WebDriver: {e}")
            print("\n🔧 Troubleshooting tips:")
            print("1. Make sure Google Chrome is installed")
            print("2. Try running: pip install --upgrade selenium webdriver-manager")
            print("3. Check if Chrome is up to date")
            raise

    def _get_chrome_user_data_dir(self):
        """Get the Chrome user data directory path"""
        try:
            if platform.system() == "Windows":
                import os
                # Common Chrome user data paths on Windows
                possible_paths = [
                    os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\User Data"),
                    os.path.expanduser("~\\AppData\\Roaming\\Google\\Chrome\\User Data"),
                    "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data"
                ]

                for path in possible_paths:
                    expanded_path = os.path.expandvars(path)
                    if os.path.exists(expanded_path):
                        return expanded_path

            return None
        except Exception as e:
            print(f"⚠️ Error finding Chrome profile: {e}")
            return None
        
    def navigate_to_shopify_orders(self, store_url):
        """
        Navigate to the Shopify orders page
        
        Args:
            store_url (str): The Shopify admin orders URL
        """
        try:
            print(f"🌐 Navigating to: {store_url}")
            self.driver.get(store_url)
            
            if self.slow_mode:
                time.sleep(3)
                
            print("✓ Successfully navigated to Shopify orders page")
            return True
            
        except Exception as e:
            print(f"❌ Error navigating to Shopify: {e}")
            return False
    
    def search_for_order(self, order_number):
        """
        Search for a specific order number
        
        Args:
            order_number (str): The order number to search for
        """
        try:
            print(f"🔍 Searching for order: {order_number}")
            
            # Wait for page to load and find search input
            # Try multiple possible selectors for the search field
            search_selectors = [
                "input[placeholder*='Search']",
                "input[type='search']",
                "input[data-testid*='search']",
                ".Polaris-TextField__Input",
                "[data-slot-identifier='search-slot-label'] + input",
                "input[aria-label*='Search']"
            ]
            
            search_input = None
            for selector in search_selectors:
                try:
                    search_input = self.wait.until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                    )
                    print(f"✓ Found search input using selector: {selector}")
                    break
                except:
                    continue
            
            if not search_input:
                # Try to find by the div structure you provided
                try:
                    # Look for the parent div and then find input within it
                    parent_div = self.driver.find_element(By.CSS_SELECTOR, "div._Pressed_1i3g1_22")
                    search_input = parent_div.find_element(By.TAG_NAME, "input")
                    print("✓ Found search input using parent div structure")
                except:
                    print("❌ Could not find search input field")
                    return False
            
            # Clear and enter the order number
            search_input.clear()
            if self.slow_mode:
                time.sleep(1)
                
            # Type the order number slowly if in slow mode
            if self.slow_mode:
                for char in order_number:
                    search_input.send_keys(char)
                    time.sleep(0.1)
            else:
                search_input.send_keys(order_number)
            
            print(f"✓ Entered order number: {order_number}")
            
            if self.slow_mode:
                time.sleep(2)
            
            # Press Enter
            search_input.send_keys(Keys.RETURN)
            print("✓ Pressed Enter to search")
            
            if self.slow_mode:
                time.sleep(3)
            
            return True
            
        except Exception as e:
            print(f"❌ Error searching for order: {e}")
            return False
    
    def wait_for_order_page(self, order_number, timeout=30):
        """
        Wait for the order page to load successfully
        
        Args:
            order_number (str): The order number to verify
            timeout (int): Maximum time to wait
        """
        try:
            print(f"⏳ Waiting for order page to load...")
            
            # Wait for order number to appear in the page
            order_indicators = [
                f"//h1[contains(text(), '{order_number}')]",
                f"//*[contains(text(), '#{order_number}')]",
                f"//*[contains(text(), '{order_number}')]"
            ]
            
            for indicator in order_indicators:
                try:
                    element = WebDriverWait(self.driver, timeout).until(
                        EC.presence_of_element_located((By.XPATH, indicator))
                    )
                    print(f"✓ Order page loaded successfully - found order {order_number}")
                    return True
                except:
                    continue
            
            # Alternative: check if URL contains the order number
            if order_number in self.driver.current_url:
                print(f"✓ Order page loaded - URL contains order number")
                return True
            
            print(f"⚠️ Could not verify order page load, but continuing...")
            return True
            
        except Exception as e:
            print(f"❌ Error waiting for order page: {e}")
            return False
    
    def extract_order_information(self):
        """
        Extract order information from the current page
        
        Returns:
            dict: Extracted order information
        """
        try:
            print("📊 Extracting order information...")
            
            # Get page source and parse with BeautifulSoup
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            order_info = {
                'url': self.driver.current_url,
                'title': self.driver.title,
                'extracted_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                'order_details': {},
                'customer_info': {},
                'items': [],
                'raw_text': soup.get_text()
            }
            
            # Extract order number
            order_number_elements = soup.find_all(text=lambda text: text and '#' in text and any(char.isdigit() for char in text))
            if order_number_elements:
                order_info['order_details']['order_number'] = order_number_elements[0].strip()
            
            # Extract customer information
            customer_sections = soup.find_all(['div', 'section'], class_=lambda x: x and 'customer' in x.lower() if x else False)
            for section in customer_sections:
                text = section.get_text(strip=True)
                if '@' in text:  # Likely contains email
                    order_info['customer_info']['raw_customer_data'] = text
            
            # Extract order items (look for product names, quantities, prices)
            item_sections = soup.find_all(['tr', 'div'], class_=lambda x: x and any(keyword in x.lower() for keyword in ['item', 'product', 'line']) if x else False)
            for section in item_sections:
                text = section.get_text(strip=True)
                if text and ('$' in text or any(char.isdigit() for char in text)):
                    order_info['items'].append(text)
            
            # Extract any monetary values
            import re
            money_pattern = r'\$[\d,]+\.?\d*'
            money_matches = re.findall(money_pattern, soup.get_text())
            if money_matches:
                order_info['order_details']['monetary_values'] = money_matches
            
            print(f"✓ Extracted order information successfully")
            return order_info
            
        except Exception as e:
            print(f"❌ Error extracting order information: {e}")
            return {'error': str(e), 'url': self.driver.current_url if self.driver else 'Unknown'}
    
    def save_order_data(self, order_info, filename=None):
        """
        Save extracted order information to a JSON file
        
        Args:
            order_info (dict): The extracted order information
            filename (str): Optional filename, defaults to order_number_timestamp.json
        """
        try:
            if not filename:
                timestamp = time.strftime('%Y%m%d_%H%M%S')
                order_num = order_info.get('order_details', {}).get('order_number', 'unknown').replace('#', '')
                filename = f"order_{order_num}_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(order_info, f, indent=2, ensure_ascii=False)
            
            print(f"✓ Order data saved to: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ Error saving order data: {e}")
            return None
    
    def close(self):
        """Close the browser and cleanup"""
        if self.driver:
            self.driver.quit()
            print("✓ Browser closed successfully")

def main():
    """Main function to run the scraper"""
    # Configuration
    SHOPIFY_ORDERS_URL = "https://admin.shopify.com/store/better-value-pharmacy-online/orders"
    ORDER_NUMBER = "18198"
    
    # Initialize scraper in slow mode for verification
    scraper = ShopifyOrderScraper(headless=False, slow_mode=True)
    
    try:
        # Setup browser
        scraper.setup_driver()
        
        # Navigate to Shopify orders page
        if not scraper.navigate_to_shopify_orders(SHOPIFY_ORDERS_URL):
            return
        
        print("\n⚠️  MANUAL STEP REQUIRED:")
        print("Please log in to Shopify admin if prompted.")
        print("Press Enter when you're ready to continue with the search...")
        input()
        
        # Search for the order
        if not scraper.search_for_order(ORDER_NUMBER):
            return
        
        # Wait for order page to load
        if not scraper.wait_for_order_page(ORDER_NUMBER):
            return
        
        # Extract order information
        order_info = scraper.extract_order_information()
        
        # Save the data
        filename = scraper.save_order_data(order_info)
        
        print(f"\n🎉 Successfully completed order extraction!")
        print(f"📁 Data saved to: {filename}")
        
        # Keep browser open for verification in slow mode
        if scraper.slow_mode:
            print("\n🔍 Browser will remain open for verification.")
            print("Press Enter to close the browser...")
            input()
        
    except KeyboardInterrupt:
        print("\n⚠️ Process interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    finally:
        scraper.close()

if __name__ == "__main__":
    main()
